#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型列表获取和可用性测试脚本
通过 OpenAI 格式 API 获取模型列表并测试可用性
"""

import requests
import json
import time
from typing import List, Dict, Any
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# API 配置
API_BASE_URL = "https://millx2api-swfk.deno.dev"
API_KEY = "sk-iwDjf9o7x6kXKUz_5Zii92Y7g1gTPQgqo0H7QaWw-47zRy407thyEyXVyr0"

# 输出文件
MODELS_FILE = "models_list.txt"
AVAILABLE_MODELS_FILE = "available_models.txt"

def get_models_list() -> List[str]:
    """获取模型列表"""
    print("正在获取模型列表...")

    # 不需要认证头
    headers = {
        "Content-Type": "application/json"
    }

    # 尝试多次请求
    for attempt in range(3):
        try:
            print(f"尝试第 {attempt + 1} 次连接...")
            response = requests.get(
                f"{API_BASE_URL}/v1/models",
                headers=headers,
                timeout=30,
                verify=False  # 忽略SSL证书验证
            )
            response.raise_for_status()

            data = response.json()
            models = []

            if "data" in data:
                for model in data["data"]:
                    if "id" in model:
                        models.append(model["id"])

            print(f"获取到 {len(models)} 个模型")
            return models

        except requests.exceptions.RequestException as e:
            print(f"第 {attempt + 1} 次尝试失败: {e}")
            if attempt < 2:
                print("等待 3 秒后重试...")
                time.sleep(3)
            else:
                print("所有尝试都失败了")
                return []
        except json.JSONDecodeError as e:
            print(f"解析响应失败: {e}")
            return []

def save_models_to_file(models: List[str], filename: str):
    """保存模型列表到文件"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            for model in models:
                f.write(f"{model}\n")
        print(f"模型列表已保存到 {filename}")
    except Exception as e:
        print(f"保存文件失败: {e}")

def test_model(model_id: str) -> bool:
    """测试单个模型是否可用"""
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }

    # 简单的测试请求
    test_data = {
        "model": model_id,
        "messages": [
            {"role": "user", "content": "Hello"}
        ],
        "max_tokens": 10,
        "temperature": 0.1
    }

    try:
        response = requests.post(
            f"{API_BASE_URL}/v1/chat/completions",
            headers=headers,
            json=test_data,
            timeout=60,
            verify=False  # 忽略SSL证书验证
        )

        # 检查响应状态
        if response.status_code == 200:
            data = response.json()
            # 检查是否有有效的响应
            if "choices" in data and len(data["choices"]) > 0:
                return True
        elif response.status_code == 400:
            # 400错误可能表示模型存在但参数有问题，仍然算作可用
            try:
                error_data = response.json()
                if "error" in error_data:
                    error_msg = error_data["error"].get("message", "")
                    # 如果是模型不存在的错误，则不可用
                    if "model" in error_msg.lower() and "not found" in error_msg.lower():
                        return False
                    # 其他400错误可能是参数问题，模型本身可用
                    return True
            except:
                pass

        return False

    except Exception as e:
        print(f"  错误: {e}")
        return False

def test_models_availability(models: List[str]) -> List[str]:
    """测试所有模型的可用性"""
    print("开始测试模型可用性...")
    available_models = []
    
    for i, model in enumerate(models, 1):
        print(f"测试模型 {i}/{len(models)}: {model}")
        
        if test_model(model):
            print(f"✓ {model} 可用")
            available_models.append(model)
        else:
            print(f"✗ {model} 不可用")
        
        # 添加延迟避免请求过快
        time.sleep(1)
    
    print(f"\n测试完成，共 {len(available_models)} 个模型可用")
    return available_models

def main():
    """主函数"""
    print("=" * 50)
    print("模型列表获取和可用性测试工具")
    print("=" * 50)
    
    # 1. 获取模型列表
    models = get_models_list()
    if not models:
        print("未能获取到模型列表，程序退出")
        return
    
    # 2. 保存模型列表到文件
    save_models_to_file(models, MODELS_FILE)
    
    # 3. 测试模型可用性
    available_models = test_models_availability(models)
    
    # 4. 保存可用模型列表到文件
    if available_models:
        save_models_to_file(available_models, AVAILABLE_MODELS_FILE)
        print(f"\n可用模型列表已保存到 {AVAILABLE_MODELS_FILE}")
    else:
        print("\n没有找到可用的模型")
    
    print("\n程序执行完成！")

if __name__ == "__main__":
    main()
